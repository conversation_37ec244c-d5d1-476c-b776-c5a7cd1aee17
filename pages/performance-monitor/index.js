// pages/performance-monitor/index.js
import optimisticUpdateManager from '../../utils/optimistic-update-manager.js'
import layeredSyncManager from '../../utils/layered-sync-manager.js'
import simpleDataManager from '../../utils/simple-data-manager.js'
import smartAvatarManager from '../../utils/avatar-cache-manager.js'
import systemDemo from '../../utils/system-demo.js'

Page({
  data: {
    // 系统状态
    systemStats: {
      optimisticUpdate: {},
      layeredSync: {},
      dataManager: {},
      avatarManager: {}
    },
    
    // 性能指标
    performanceMetrics: {
      cacheHitRate: 0,
      avgResponseTime: 0,
      syncQueueSize: 0,
      memoryUsage: 0
    },
    
    // 实时日志
    logs: [],
    maxLogs: 50,
    
    // 演示状态
    demoRunning: false,
    
    // 刷新状态
    refreshing: false
  },

  onLoad() {
    this.initPage()
    this.startRealTimeMonitor()
  },

  onUnload() {
    this.stopRealTimeMonitor()
  },

  // 初始化页面
  initPage() {
    this.addLog('性能监控页面初始化')
    this.refreshStats()
  },

  // 启动实时监控
  startRealTimeMonitor() {
    this.monitorTimer = setInterval(() => {
      this.refreshStats()
    }, 2000) // 每2秒刷新一次
  },

  // 停止实时监控
  stopRealTimeMonitor() {
    if (this.monitorTimer) {
      clearInterval(this.monitorTimer)
      this.monitorTimer = null
    }
  },

  // 刷新统计数据
  refreshStats() {
    try {
      // 获取各系统状态
      const optimisticStats = optimisticUpdateManager.getSyncStatus()
      const layeredStats = layeredSyncManager.getSyncStatus()
      const dataStats = simpleDataManager.getCacheStatus()
      
      // 计算性能指标
      const cacheHitRate = this.calculateCacheHitRate(dataStats)
      const syncQueueSize = optimisticStats.queueSize + layeredStats.total
      const memoryUsage = this.calculateMemoryUsage(dataStats)

      this.setData({
        systemStats: {
          optimisticUpdate: optimisticStats,
          layeredSync: layeredStats,
          dataManager: dataStats,
          avatarManager: this.getAvatarStats()
        },
        performanceMetrics: {
          cacheHitRate,
          avgResponseTime: this.calculateAvgResponseTime(),
          syncQueueSize,
          memoryUsage
        }
      })

    } catch (error) {
      this.addLog(`刷新统计失败: ${error.message}`)
    }
  },

  // 计算缓存命中率
  calculateCacheHitRate(dataStats) {
    if (!dataStats.memorySize) return 0
    return Math.min(dataStats.memorySize / dataStats.maxMemorySize * 100, 100)
  },

  // 计算内存使用率
  calculateMemoryUsage(dataStats) {
    return Math.round(dataStats.memorySize / dataStats.maxMemorySize * 100)
  },

  // 计算平均响应时间
  calculateAvgResponseTime() {
    // 模拟计算，实际应该从性能管理器获取
    return Math.round(Math.random() * 100 + 50) // 50-150ms
  },

  // 获取头像管理器状态
  getAvatarStats() {
    try {
      return smartAvatarManager.getCacheStats()
    } catch (error) {
      return { memoryCache: 0, storageCache: 0, loadingCount: 0 }
    }
  },

  // 添加日志
  addLog(message) {
    const timestamp = new Date().toLocaleTimeString()
    const newLog = `[${timestamp}] ${message}`
    
    const logs = [...this.data.logs, newLog]
    if (logs.length > this.data.maxLogs) {
      logs.shift() // 移除最旧的日志
    }
    
    this.setData({ logs })
  },

  // 运行系统演示
  async runDemo() {
    if (this.data.demoRunning) return

    this.setData({ demoRunning: true })
    this.addLog('开始运行系统演示...')

    try {
      await systemDemo.runFullDemo()
      this.addLog('系统演示完成')
    } catch (error) {
      this.addLog(`演示失败: ${error.message}`)
    } finally {
      this.setData({ demoRunning: false })
    }
  },

  // 清理缓存
  clearCache() {
    try {
      simpleDataManager.clearCache()
      smartAvatarManager.clearAllCache()
      this.addLog('缓存清理完成')
      this.refreshStats()
    } catch (error) {
      this.addLog(`缓存清理失败: ${error.message}`)
    }
  },

  // 强制同步
  async forceSync() {
    this.addLog('开始强制同步...')
    try {
      await layeredSyncManager.forceSync()
      this.addLog('强制同步完成')
      this.refreshStats()
    } catch (error) {
      this.addLog(`强制同步失败: ${error.message}`)
    }
  },

  // 手动刷新
  onRefresh() {
    this.setData({ refreshing: true })
    this.addLog('手动刷新统计数据')
    
    setTimeout(() => {
      this.refreshStats()
      this.setData({ refreshing: false })
    }, 500)
  },

  // 清空日志
  clearLogs() {
    this.setData({ logs: [] })
    this.addLog('日志已清空')
  },

  // 测试乐观更新
  async testOptimisticUpdate() {
    this.addLog('测试乐观更新...')
    
    const testData = {
      id: Date.now(),
      action: 'test',
      data: { value: Math.random() }
    }

    const uiUpdateFn = async (data) => {
      this.addLog(`UI更新: ${JSON.stringify(data)}`)
      return null
    }

    const syncFn = async (data) => {
      // 模拟网络延迟
      await new Promise(resolve => setTimeout(resolve, 1000))
      this.addLog(`同步完成: ${data.action}`)
      return { success: true }
    }

    try {
      const result = await optimisticUpdateManager.optimisticUpdate(
        'test',
        testData,
        uiUpdateFn,
        syncFn
      )
      this.addLog(`乐观更新结果: ${JSON.stringify(result)}`)
    } catch (error) {
      this.addLog(`乐观更新失败: ${error.message}`)
    }
  },

  // 测试分层同步
  async testLayeredSync() {
    this.addLog('测试分层同步...')
    
    const actions = ['addRecord', 'updateStatistics', 'deleteRecord']
    
    for (const action of actions) {
      const uiUpdateFn = async () => {
        this.addLog(`${action} UI更新`)
        return null
      }

      const syncFn = async () => {
        await new Promise(resolve => setTimeout(resolve, 500))
        this.addLog(`${action} 同步完成`)
        return { success: true }
      }

      await layeredSyncManager.layeredSync(
        action,
        { test: true },
        syncFn,
        uiUpdateFn
      )
    }
  },

  // 测试数据管理器
  async testDataManager() {
    this.addLog('测试数据管理器...')
    
    const testKey = 'test_data'
    const testData = { value: Date.now(), random: Math.random() }
    
    // 测试数据存储和获取
    const result1 = await simpleDataManager.getData(
      testKey,
      async () => {
        this.addLog('模拟云端数据获取')
        await new Promise(resolve => setTimeout(resolve, 800))
        return { success: true, data: testData }
      }
    )
    this.addLog(`第一次获取: ${result1.fromCache ? '缓存' : '云端'}`)
    
    // 第二次获取应该从缓存返回
    const result2 = await simpleDataManager.getData(testKey)
    this.addLog(`第二次获取: ${result2.fromCache ? '缓存' : '云端'}`)
  },

  // 获取性能等级
  getPerformanceLevel(value, thresholds = [30, 60, 80]) {
    if (value < thresholds[0]) return 'excellent'
    if (value < thresholds[1]) return 'good'
    if (value < thresholds[2]) return 'fair'
    return 'poor'
  },

  // 格式化数字
  formatNumber(num) {
    if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K'
    }
    return num.toString()
  },

  // 格式化时间
  formatTime(timestamp) {
    if (!timestamp) return '未知'
    const date = new Date(timestamp)
    return date.toLocaleTimeString()
  }
})

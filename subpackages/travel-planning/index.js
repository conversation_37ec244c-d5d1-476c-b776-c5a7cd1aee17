// subpackages/travel-planning/index.js

const app = getApp()
const simpleDataManager = require('../../utils/simple-data-manager.js').default
const layeredSyncManager = require('../../utils/layered-sync-manager.js').default
const hapticManager = require('../../utils/haptic-manager.js').default

Page({
  data: {
    // 旅游数据分析
    analytics: {
      overview: {
        totalPlans: 0,
        activePlans: 0,
        completedPlans: 0,
        planningPlans: 0,
        totalExpenses: 0,
        totalBudget: 0,
        budgetUsage: 0
      },
      destinations: [],
      monthlyTrends: [],
      durationDistribution: [],
      budgetAnalysis: []
    },

    // 当前计划
    currentPlan: null,

    // 正在进行的计划列表
    ongoingPlans: [],

    // 计划列表
    plans: [],

    // 可用计划（用于快速记录）
    availablePlans: [],

    // 加载状态
    loading: false,
    mapGenerating: false, // 地图生成状态

    // 剪贴板检测
    processedInviteCodes: [], // 已处理的邀请码，避免重复弹窗
    clipboardPermissionGranted: false, // 是否已授权剪贴板访问

    // 分层加载状态
    loadingStates: {
      analytics: false,
      currentPlan: false,
      ongoingPlans: false,
      plans: false
    },

    // 计划选择弹窗
    showPlanSelector: false,

    // 页面状态管理
    _lastLoadTime: 0,
    _needsRefresh: false
  },

  async onLoad() {
    // 初始化简化数据管理器
    await simpleDataManager.initialize()

    // 自动更新所有计划状态
    this.autoUpdateAllPlanStatus()

    // 加载旅行数据
    this.loadTravelData()
  },



  onShow() {
    // 标记页面可见
    this.setData({ _isVisible: true })

    // 检查全局刷新标记
    const needsRefreshFromGlobal = this.checkGlobalRefreshFlag()

    // 智能刷新策略：只在必要时刷新
    const now = Date.now()
    const lastLoadTime = this.data._lastLoadTime || 0
    const timeSinceLastLoad = now - lastLoadTime

    // 如果有全局刷新标记，立即刷新；否则按时间间隔刷新
    if (needsRefreshFromGlobal || timeSinceLastLoad > 30 * 1000 || this.data._needsRefresh) {
      this.loadTravelData(true)
      this.setData({
        _lastLoadTime: now,
        _needsRefresh: false
      })
    }

    // {{ AURA-X: Remove - 移除自动剪切板检测，改为用户主动触发. Approval: 寸止(ID:1738056000). }}
    // 移除自动剪切板检测，避免频繁弹窗影响用户体验

    // {{ AURA-X: Add - 设置实时同步. Approval: 寸止(ID:1738056000). }}
    // 设置实时同步监听器
    this.setupRealtimeSync()
  },

  onHide() {
    // 标记页面不可见
    this.setData({ _isVisible: false })
  },

  onUnload() {
    // 简化数据管理器自动清理监听器
  },

  onPullDownRefresh() {
    this.loadTravelData(true).then(() => {
      wx.stopPullDownRefresh()
    })
  },

  // 加载旅行数据 - 分层渐进式加载
  async loadTravelData(forceRefresh = false) {
    // 先检查缓存，如果有缓存数据先显示
    if (!forceRefresh) {
      const cachedData = this.loadFromCache()
      if (cachedData) {
        this.setData({ ...cachedData, loading: false })
        // 后台静默刷新关键数据
        this.backgroundRefresh()
        return
      }
    }

    // 分层加载策略
    await this.layeredDataLoad(forceRefresh)
  },

  // 分层数据加载
  async layeredDataLoad(forceRefresh = false) {
    // 第一层：立即显示骨架和基础数据
    this.setData({
      loading: true,
      loadingStates: {
        analytics: true,
        currentPlan: true,
        ongoingPlans: true,
        plans: true
      }
    })

    try {
      // 如果是强制刷新，清理本地缓存
      if (forceRefresh) {
        this.clearLocalCache()
      }

      // 第二层：优先加载关键数据（当前计划和统计）
      const criticalPromises = [
        this.loadTravelStatistics(forceRefresh).then(data => {
          this.setData({
            analytics: data,
            'loadingStates.analytics': false
          })
          return { type: 'analytics', data }
        }),
        this.loadCurrentPlan(forceRefresh).then(data => {
          this.setData({
            currentPlan: data,
            'loadingStates.currentPlan': false
          })
          return { type: 'currentPlan', data }
        })
      ]

      // 第三层：加载次要数据
      const secondaryPromises = [
        this.loadOngoingPlans(forceRefresh).then(data => {
          this.setData({
            ongoingPlans: data,
            'loadingStates.ongoingPlans': false
          })
          return { type: 'ongoingPlans', data }
        }),
        this.loadTravelPlans(forceRefresh).then(data => {
          this.setData({
            plans: data ? data.slice(0, 5) : [],
            'loadingStates.plans': false
          })
          return { type: 'plans', data }
        })
      ]

      // 先等待关键数据，再加载次要数据
      const criticalResults = await Promise.allSettled(criticalPromises)
      const secondaryResults = await Promise.allSettled(secondaryPromises)

      // 合并所有结果用于缓存
      const allResults = [...criticalResults, ...secondaryResults]
      let processedData = this.processLoadResults(allResults)

      // {{ AURA-X: Modify - 移除数据验证，依赖现有同步机制保证数据一致性. Approval: 寸止(ID:1738056000). }}
      // 简化数据验证，只检查基本结构
      if (!processedData || Object.keys(processedData).length === 0) {
        // 数据异常，使用默认数据
        processedData = this.getDefaultAnalytics()
      }

      // 保存到本地缓存
      this.saveToCache(processedData)

    } catch (error) {
      // 设置默认数据，确保页面可用
      this.setDefaultData()
    } finally {
      this.setData({ loading: false })
    }
  },

  // 后台静默刷新
  async backgroundRefresh() {
    try {
      // 只刷新关键数据，不影响UI
      const promises = [
        this.loadTravelStatistics(true),
        this.loadCurrentPlan(true)
      ]

      const results = await Promise.allSettled(promises)

      // 静默更新数据
      if (results[0].status === 'fulfilled') {
        this.setData({ analytics: results[0].value })
      }
      if (results[1].status === 'fulfilled') {
        this.setData({ currentPlan: results[1].value })
      }

      // 更新缓存
      // 数据已更新，无需额外缓存操作

    } catch (error) {
      // 静默处理后台刷新失败
    }
  },

  // 处理加载结果
  processLoadResults(results) {
    const processedData = {
      analytics: this.getDefaultAnalytics(),
      currentPlan: null,
      ongoingPlans: [],
      plans: []
    }

    results.forEach(result => {
      if (result.status === 'fulfilled' && result.value) {
        const { type, data } = result.value
        switch (type) {
          case 'analytics':
            processedData.analytics = data || this.getDefaultAnalytics()
            break
          case 'currentPlan':
            processedData.currentPlan = data
            break
          case 'ongoingPlans':
            processedData.ongoingPlans = data || []
            break
          case 'plans':
            processedData.plans = data ? data.slice(0, 5) : []
            break
        }
      }
    })

    return processedData
  },

  // 设置默认数据
  setDefaultData() {
    this.setData({
      analytics: this.getDefaultAnalytics(),
      currentPlan: null,
      ongoingPlans: [],
      plans: [],
      loadingStates: {
        analytics: false,
        currentPlan: false,
        ongoingPlans: false,
        plans: false
      }
    })
  },



  // 格式化统计数据
  formatStatisticsData(data) {
    return {
      totalPlans: data.totalPlans || 0,
      activePlans: data.ongoingPlans || 0,
      completedPlans: data.completedPlans || 0,
      planningPlans: data.plannedPlans || 0,
      totalExpenses: data.totalExpense || 0,
      totalBudget: data.travelBudget || 0,
      budgetUsage: data.budgetUsage || 0
    }
  },

  // 从缓存加载数据 - 优化缓存策略
  loadFromCache() {
    try {
      const cacheKey = 'travel_page_data'
      const cached = wx.getStorageSync(cacheKey)

      if (cached && cached.timestamp) {
        const age = Date.now() - cached.timestamp
        // 缓存3分钟内有效，确保数据及时更新
        if (age < 3 * 60 * 1000) {
          return cached.data
        }
      }
      return null
    } catch (error) {
      return null
    }
  },

  // 保存数据到缓存
  saveToCache(data) {
    try {
      const cacheKey = 'travel_page_data'
      const cacheData = {
        data: data,
        timestamp: Date.now()
      }
      wx.setStorageSync(cacheKey, cacheData)
    } catch (error) {
      // 静默处理缓存保存失败
    }
  },

  // 处理加载结果
  processLoadResults(results) {
    const processedData = {}

    results.forEach(result => {
      if (result.status === 'fulfilled' && result.value) {
        const { type, data } = result.value
        if (type && data !== undefined) {
          processedData[type] = data
        }
      }
    })

    return processedData
  },

  // 设置默认数据
  setDefaultData() {
    this.setData({
      analytics: this.getDefaultAnalytics(),
      currentPlan: null,
      ongoingPlans: [],
      plans: [],
      loading: false
    })
  },



  // 清理本地缓存
  clearLocalCache() {
    try {
      const cacheKeys = [
        'travel_statistics',
        'travel_plans_cache',
        'ongoing_plans_cache',
        'travel_page_data'
      ]

      cacheKeys.forEach(key => {
        try {
          wx.removeStorageSync(key)
        } catch (error) {
          // 静默处理单个缓存清理失败
        }
      })
    } catch (error) {
      // 静默处理缓存清理失败
    }
  },

  // 加载统计数据 - 使用本地计算避免云函数调用
  async loadTravelStatistics(forceRefresh = false) {
    try {
      // {{ AURA-X: Modify - 使用本地计算替代云函数调用. Approval: 寸止(ID:1738056000). }}
      // 使用 simpleDataManager.calculateLocally 完全本地计算
      const result = simpleDataManager.calculateLocally(
        'travel_statistics',
        () => {
          const plans = simpleDataManager.getFromLocalStorage('travel_plans_cache') || []
          const expenses = simpleDataManager.getFromLocalStorage('expense_records_cache') || []

          // 本地计算统计数据
          const stats = {
            totalPlans: plans.length,
            ongoingPlans: plans.filter(p => p.status === 'ongoing').length,
            completedPlans: plans.filter(p => p.status === 'completed').length,
            plannedPlans: plans.filter(p => p.status === 'planning').length,
            totalExpense: expenses.reduce((sum, e) => sum + (e.amount || 0), 0),
            travelBudget: plans.reduce((sum, p) => sum + (p.budget || 0), 0),
            budgetUsage: 0
          }

          // 计算预算使用率
          if (stats.travelBudget > 0) {
            stats.budgetUsage = (stats.totalExpense / stats.travelBudget) * 100
          }

          return { success: true, data: stats }
        },
        ['travel_plans_cache', 'expense_records_cache']
      )

      if (result && result.success && result.data) {
        const data = result.data

        return {
          overview: {
            totalPlans: data.totalPlans || 0,
            activePlans: data.ongoingPlans || 0,
            completedPlans: data.completedPlans || 0,
            planningPlans: data.plannedPlans || 0,
            totalExpenses: data.totalExpense || 0,
            totalBudget: data.travelBudget || 0,
            budgetUsage: data.budgetUsage || 0
          }
        }
      } else {
        return this.getDefaultAnalytics()
      }
    } catch (error) {
      console.error('加载旅行统计失败:', error)
      return this.getDefaultAnalytics()
    }
  },

  // 获取默认统计数据
  getDefaultAnalytics() {
    return {
      overview: {
        totalPlans: 0,
        activePlans: 0,
        completedPlans: 0,
        planningPlans: 0,
        totalExpenses: 0,
        totalBudget: 0,
        budgetUsage: 0
      }
    }
  },

  // 加载当前计划 - 使用本地缓存避免云函数调用
  async loadCurrentPlan(forceRefresh = false) {
    try {
      // {{ AURA-X: Modify - 使用本地缓存替代云函数调用. Approval: 寸止(ID:1738056000). }}
      // 从本地缓存获取旅行计划，筛选进行中的计划
      const plans = simpleDataManager.getFromLocalStorage('travel_plans_cache') || []
      const ongoingPlans = plans.filter(plan => plan.status === 'ongoing')

      if (ongoingPlans.length > 0) {
        // 返回最早开始的进行中计划
        const currentPlan = ongoingPlans.sort((a, b) =>
          new Date(a.startDate) - new Date(b.startDate)
        )[0]

        // 格式化当前计划数据
        return this.formatPlanData(currentPlan)
      }

      return null
    } catch (error) {
      console.error('加载当前计划失败:', error)
      return null
    }
  },

  // 加载正在进行的计划 - 使用新的数据管理器
  async loadOngoingPlans(forceRefresh = false) {
    try {
      const result = await dataManager.getTravelPlans({
        status: 'ongoing',
        forceRefresh
      })

      if (result && result.success && result.data) {
        // 格式化计划数据
        const formattedPlans = result.data.map(plan => this.formatPlanData(plan))
        return formattedPlans
      }

      return []
    } catch (error) {
      console.error('加载进行中计划失败:', error)
      return []
    }
  },

  // 加载所有计划 - 使用新的数据管理器
  async loadTravelPlans(forceRefresh = false) {
    try {
      const result = await dataManager.getTravelPlans({
        limit: 5,
        forceRefresh
      })

      if (result && result.success && result.data) {
        // 格式化计划数据
        const formattedPlans = result.data.map(plan => this.formatPlanData(plan))
        return formattedPlans
      }

      return []
    } catch (error) {
      console.error('加载旅行计划失败:', error)
      return []
    }
  },

  // 查看当前计划详情
  viewCurrentPlan() {
    if (!this.data.currentPlan) return

    wx.navigateTo({
      url: `/subpackages/travel-planning/plan-detail/index?id=${this.data.currentPlan._id || this.data.currentPlan.id}`
    })
  },

  // 查看行程 - 当前旅程卡片
  viewItinerary(e) {
    const plan = e?.currentTarget?.dataset?.plan || this.data.currentPlan
    if (!plan) return

    hapticManager.navigation()
    wx.navigateTo({
      url: `/subpackages/travel-planning/plan-detail/index?id=${plan._id || plan.id}&tab=itinerary`
    })
  },

  // 查看地图 - 当前旅程卡片
  async viewMap(e) {
    const plan = e?.currentTarget?.dataset?.plan || this.data.currentPlan
    if (!plan) return

    // 防重复调用
    if (this.data.mapGenerating) {
      wx.showToast({
        title: '地图生成中，请稍候',
        icon: 'none'
      })
      return
    }

    hapticManager.navigation()

    // 如果计划有地图数据，直接跳转到地图页面
    if (plan.mapData) {
      wx.navigateTo({
        url: `/subpackages/travel-planning/map-view/index?data=${encodeURIComponent(JSON.stringify(plan.mapData))}`
      })
      return
    }

    // 设置生成状态
    this.setData({ mapGenerating: true })

    // 尝试生成地图数据
    wx.showLoading({ title: '生成地图中...' })

    try {
      let attractions = []

      // 优先从行程中提取景点信息
      if (plan.itinerary && plan.itinerary.length > 0) {
        plan.itinerary.forEach(day => {
          if (day.activities) {
            day.activities.forEach(activity => {
              if (activity.location && activity.coordinates) {
                attractions.push({
                  name: activity.location,
                  coordinates: activity.coordinates,
                  description: activity.description || activity.activity
                })
              }
            })
          }
        })
      }

      // 如果没有行程数据，尝试调用AI获取目的地景点（如果云函数可用）
      if (attractions.length === 0 && plan.destination) {
        // 先创建基础标记，确保总是有地图数据
        const defaultAttraction = {
          name: plan.destination,
          coordinates: plan.coordinates || {
            latitude: 39.908823,
            longitude: 116.397470
          },
          description: `${plan.destination}旅行计划`
        }
        attractions.push(defaultAttraction)

        // 尝试获取AI推荐景点（非阻塞）
        try {
          console.log('尝试调用AI获取景点推荐:', plan.destination)
          const attractionsResult = await wx.cloud.callFunction({
            name: 'ai-travel',
            data: {
              action: 'getAttractions',
              data: {
                destination: plan.destination,
                preferences: ['热门景点', '必游景点']
              }
            }
          })

          console.log('AI景点推荐结果:', attractionsResult)

          if (attractionsResult.result.success && attractionsResult.result.data && attractionsResult.result.data.attractions) {
            // AI推荐成功，替换默认标记
            attractions = attractionsResult.result.data.attractions.slice(0, 8) // 取前8个景点
            console.log('使用AI推荐的景点')
          } else {
            console.log('AI推荐返回空结果，使用默认标记')
          }
        } catch (aiError) {
          console.error('AI景点推荐调用失败，使用默认标记:', aiError)
          // 保持默认标记，不影响地图生成
        }
      }

      // 生成地图数据
      if (attractions.length > 0) {
        try {
          console.log('调用generateMapData，景点数量:', attractions.length)
          const result = await wx.cloud.callFunction({
            name: 'ai-travel',
            data: {
              action: 'generateMapData',
              data: {
                attractions: attractions,
                routes: []
              }
            }
          })

          wx.hideLoading()
          this.setData({ mapGenerating: false })

          if (result.result.success && result.result.data) {
            // 生成成功，跳转到地图页面
            console.log('地图数据生成成功')
            wx.navigateTo({
              url: `/subpackages/travel-planning/map-view/index?data=${encodeURIComponent(JSON.stringify(result.result.data))}`
            })
            return
          } else {
            console.log('地图数据生成失败，使用简单地图')
            // 生成失败，创建简单的地图数据
            const simpleMapData = {
              center: attractions[0].coordinates,
              scale: 12,
              markers: attractions.map((attraction, index) => ({
                id: index,
                latitude: attraction.coordinates.latitude,
                longitude: attraction.coordinates.longitude,
                title: attraction.name,
                iconPath: '/images/marker.png',
                width: 30,
                height: 30
              })),
              polyline: []
            }

            wx.navigateTo({
              url: `/subpackages/travel-planning/map-view/index?data=${encodeURIComponent(JSON.stringify(simpleMapData))}`
            })
            return
          }
        } catch (mapError) {
          console.error('地图数据生成调用失败:', mapError)
          wx.hideLoading()
          this.setData({ mapGenerating: false })

          // 创建最简单的地图数据
          const basicMapData = {
            center: attractions[0].coordinates,
            scale: 12,
            markers: [{
              id: 0,
              latitude: attractions[0].coordinates.latitude,
              longitude: attractions[0].coordinates.longitude,
              title: attractions[0].name,
              iconPath: '/images/marker.png',
              width: 30,
              height: 30
            }],
            polyline: []
          }

          wx.navigateTo({
            url: `/subpackages/travel-planning/map-view/index?data=${encodeURIComponent(JSON.stringify(basicMapData))}`
          })
          return
        }
      }

      // 如果没有景点数据或生成失败，跳转到详情页面
      wx.hideLoading()
      this.setData({ mapGenerating: false })
      wx.showToast({
        title: '暂无地图数据',
        icon: 'none'
      })
      wx.navigateTo({
        url: `/subpackages/travel-planning/plan-detail/index?id=${plan._id || plan.id}&tab=map`
      })

    } catch (error) {
      wx.hideLoading()
      this.setData({ mapGenerating: false })
      console.error('生成地图数据失败:', error)
      wx.showToast({
        title: '地图生成失败',
        icon: 'none'
      })
      // 跳转到详情页面作为备选方案
      wx.navigateTo({
        url: `/subpackages/travel-planning/plan-detail/index?id=${plan._id || plan.id}&tab=map`
      })
    }
  },

  // 记录花费
  recordExpense(e) {
    const plan = e.currentTarget.dataset.plan
    wx.navigateTo({
      url: `/subpackages/account/travel-expense/index?mode=travel&planId=${plan._id || plan.id}`
    })
  },

  // 查看行程
  viewItinerary(e) {
    const plan = e.currentTarget.dataset.plan
    wx.navigateTo({
      url: `/subpackages/travel-planning/itinerary/index?planId=${plan._id || plan.id}`
    })
  },

  // 创建新计划
  createNewPlan() {
    hapticManager.navigation()
    wx.navigateTo({
      url: '/subpackages/travel-planning/create-plan/index'
    })
  },

  // 快速创建计划 - 顶部横幅按钮
  quickCreatePlan() {
    hapticManager.navigation()
    wx.navigateTo({
      url: '/subpackages/travel-planning/create-plan/index'
    })
  },

  // 使用魔法解析创建计划
  createPlanWithMagic() {
    hapticManager.navigation()
    wx.navigateTo({
      url: '/subpackages/travel-planning/create-plan/index?mode=magic'
    })
  },

  // 使用AI创建计划
  createPlanWithAI() {
    hapticManager.navigation()
    wx.navigateTo({
      url: '/subpackages/travel-planning/create-plan/index?mode=ai'
    })
  },

  // 手动创建计划
  createPlanManual() {
    hapticManager.navigation()
    wx.navigateTo({
      url: '/subpackages/travel-planning/create-plan/index?mode=manual'
    })
  },

  // 加入计划
  joinPlan() {
    hapticManager.navigation()
    wx.navigateTo({
      url: '/subpackages/travel-planning/join-plan/index'
    })
  },

  // 邀请协作
  inviteCollaboration() {
    // 检查是否有任何计划
    if (!this.data.plans || this.data.plans.length === 0) {
      wx.showModal({
        title: '提示',
        content: '请先创建一个旅行计划，然后才能邀请好友协作',
        showCancel: false,
        confirmText: '知道了'
      })
      return
    }

    // 如果有当前计划，直接跳转
    if (this.data.currentPlan) {
      wx.navigateTo({
        url: `/subpackages/travel-planning/plan-detail/index?id=${this.data.currentPlan._id || this.data.currentPlan.id}`
      })
      return
    }

    // 如果没有当前计划但有其他计划，让用户选择
    const planOptions = this.data.plans.map(plan => ({
      name: plan.title,
      value: plan._id || plan.id,
      collaboration: plan.collaboration
    }))

    wx.showActionSheet({
      itemList: planOptions.map(option => option.name),
      success: async (res) => {
        const selectedPlan = planOptions[res.tapIndex]

        // 检查计划是否开启了协作
        if (!selectedPlan.collaboration?.enabled) {
          // 如果没有开启协作，先开启
          await this.enablePlanCollaboration(selectedPlan.value)
        } else {
          // 如果已开启协作，直接显示邀请弹窗
          this.showPlanInviteModal(selectedPlan.collaboration.inviteCode, selectedPlan.name)
        }
      }
    })
  },

  // 检测剪贴板中的邀请内容
  async checkClipboardForInvite() {
    // 如果已经授权过，直接检测
    if (this.data.clipboardPermissionGranted) {
      await this.performClipboardCheck()
      return
    }

    try {
      // 先尝试获取剪贴板内容，检查是否包含邀请格式
      const clipboardData = await wx.getClipboardData()
      const clipboardText = clipboardData.data

      // 检查是否包含邀请内容
      const invitePattern = /📋 邀请码：([A-Z0-9]{6})/
      const match = clipboardText.match(invitePattern)

      if (match && match[1]) {
        // 发现邀请内容，询问用户是否允许检测剪贴板
        wx.showModal({
          title: '发现邀请协作',
          content: '检测到剪贴板中可能有旅行协作邀请，是否允许小程序读取剪贴板内容以便自动识别邀请？',
          confirmText: '允许',
          cancelText: '不允许',
          success: (res) => {
            if (res.confirm) {
              // 用户同意，标记权限并执行检测
              this.setData({ clipboardPermissionGranted: true })
              this.performClipboardCheck()
            }
          }
        })
      }
    } catch (error) {
      // 静默处理错误，不影响正常使用
      console.log('剪贴板检测失败:', error)
    }
  },

  // 执行剪贴板检测
  async performClipboardCheck() {
    try {
      const clipboardData = await wx.getClipboardData()
      const clipboardText = clipboardData.data

      // 检查是否包含邀请内容
      const invitePattern = /📋 邀请码：([A-Z0-9]{6})/
      const match = clipboardText.match(invitePattern)

      if (match && match[1]) {
        const inviteCode = match[1]

        // 检查是否已经处理过这个邀请码
        if (this.data.processedInviteCodes.includes(inviteCode)) {
          return
        }

        // 标记为已处理
        this.setData({
          processedInviteCodes: [...this.data.processedInviteCodes, inviteCode]
        })

        // 弹窗询问用户是否加入协作
        wx.showModal({
          title: '发现邀请协作',
          content: `检测到剪贴板中有旅行协作邀请，邀请码：${inviteCode}，是否立即加入？`,
          confirmText: '立即加入',
          cancelText: '稍后再说',
          success: (res) => {
            if (res.confirm) {
              // 跳转到加入计划页面，并自动填入邀请码
              wx.navigateTo({
                url: `/subpackages/travel-planning/join-plan/index?inviteCode=${inviteCode}`
              })
            }
          }
        })
      }
    } catch (error) {
      console.log('剪贴板检测失败:', error)
    }
  },

  // 开启计划协作
  async enablePlanCollaboration(planId) {
    try {
      wx.showLoading({ title: '开启协作中...' })

      const result = await wx.cloud.callFunction({
        name: 'travel',
        data: {
          action: 'enableCollaboration',
          data: { planId }
        }
      })

      wx.hideLoading()

      if (result.result.success) {
        const { inviteCode } = result.result.data
        const plan = this.data.plans.find(p => (p._id || p.id) === planId)
        this.showPlanInviteModal(inviteCode, plan?.title || '旅行计划')
      } else {
        wx.showToast({
          title: result.result.message || '开启协作失败',
          icon: 'none'
        })
      }
    } catch (error) {
      wx.hideLoading()
      wx.showToast({
        title: '开启协作失败',
        icon: 'none'
      })
    }
  },

  // 显示计划邀请弹窗
  showPlanInviteModal(inviteCode, planTitle) {
    const link = `🎒 邀请你一起规划旅行：${planTitle}

📋 邀请码：${inviteCode}

💡 使用方法：
1. 打开"爱巢小记"小程序
2. 在旅行规划页面点击"加入计划"
3. 输入邀请码即可加入协作

快来一起规划精彩的旅程吧！✨`

    wx.showModal({
      title: '邀请好友协作',
      content: `邀请码：${inviteCode}\n\n点击"复制邀请"将邀请信息复制到剪贴板，发送给好友即可邀请协作。`,
      confirmText: '复制邀请',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          wx.setClipboardData({
            data: link,
            success: () => {
              wx.showToast({
                title: '邀请信息已复制',
                icon: 'success'
              })
            }
          })
        }
      }
    })
  },

  // 快速记录
  quickRecord() {
    // 获取所有可用的计划（包括规划中和进行中的）
    const availablePlans = this.getAvailablePlans()

    if (availablePlans.length === 0) {
      wx.showToast({
        title: '请先创建旅行计划',
        icon: 'none'
      })
      return
    }

    if (availablePlans.length === 1) {
      // 只有一个可用计划，直接跳转
      wx.navigateTo({
        url: `/subpackages/account/travel-expense/index?mode=travel&planId=${availablePlans[0]._id || availablePlans[0].id}`
      })
    } else {
      // 多个可用计划，显示选择器
      this.setData({
        showPlanSelector: true,
        availablePlans: availablePlans
      })
    }
  },

  // 获取可用的计划（规划中和进行中的）
  getAvailablePlans() {
    const allPlans = this.data.plans || []
    const ongoingPlans = this.data.ongoingPlans || []

    // 合并规划中和进行中的计划，去重
    const planMap = new Map()

    // 添加进行中的计划
    ongoingPlans.forEach(plan => {
      planMap.set(plan._id || plan.id, plan)
    })

    // 添加规划中的计划
    allPlans.forEach(plan => {
      if (plan.status === 'planning' || plan.status === 'ongoing') {
        planMap.set(plan._id || plan.id, plan)
      }
    })

    return Array.from(planMap.values())
  },

  // 选择计划进行记录
  selectPlanForRecord(e) {
    const planId = e.currentTarget.dataset.planId
    this.setData({ showPlanSelector: false })
    wx.navigateTo({
      url: `/subpackages/account/travel-expense/index?mode=travel&planId=${planId}`
    })
  },

  // 关闭计划选择器
  closePlanSelector() {
    this.setData({ showPlanSelector: false })
  },

  // 格式化计划数据
  formatPlanData(plan) {
    if (!plan) return null

    // 格式化日期范围
    const dateRange = this.formatDateRange(plan.startDate || plan.dates?.startDate, plan.endDate || plan.dates?.endDate)

    // 格式化状态文本
    const statusMap = {
      'planning': '规划中',
      'ongoing': '进行中',
      'completed': '已完成',
      'cancelled': '已取消'
    }

    // 格式化参与人数 - 修复 [object Object] 问题
    let participants = 1
    if (plan.participants) {
      if (typeof plan.participants === 'object' && plan.participants.count) {
        participants = Number(plan.participants.count) || 1
      } else if (typeof plan.participants === 'number') {
        participants = Number(plan.participants) || 1
      }
    } else if (plan.participantCount) {
      participants = Number(plan.participantCount) || 1
    }



    // 格式化预算
    let budget = 0
    if (plan.budgetDetail && plan.budgetDetail.total) {
      budget = Number(plan.budgetDetail.total) || 0
    } else if (plan.budget) {
      budget = Number(plan.budget) || 0
    }

    // 计算进度百分比（仅对进行中的计划）
    let progressPercent = 0
    if (plan.status === 'ongoing') {
      const startDate = new Date(plan.startDate || plan.dates?.startDate)
      const endDate = new Date(plan.endDate || plan.dates?.endDate)
      const now = new Date()

      if (now >= startDate && now <= endDate) {
        const totalDays = Math.ceil((endDate - startDate) / (1000 * 60 * 60 * 24))
        const passedDays = Math.ceil((now - startDate) / (1000 * 60 * 60 * 24))
        progressPercent = Math.min(Math.round((passedDays / totalDays) * 100), 100)
      }
    }

    // {{ AURA-X: Add - 添加用户角色信息. Approval: 寸止(ID:1738056000). }}
    // 确定用户角色和角色文本
    const userRole = plan.userRole || 'creator'
    const roleText = userRole === 'creator' ? '创建者' : '协作者'

    return {
      ...plan,
      id: plan._id || plan.id,
      dateRange,
      statusText: statusMap[plan.status] || '未知',
      participants, // 修复后的人数
      duration: plan.duration || plan.dates?.duration || this.calculateDuration(plan.startDate || plan.dates?.startDate, plan.endDate || plan.dates?.endDate),
      budget: budget.toFixed(0),
      progressPercent,
      // 为当前计划添加额外的格式化字段
      dayProgress: plan.status === 'ongoing' ? Math.ceil(progressPercent * (plan.duration || 1) / 100) : 0,
      totalDays: plan.duration || plan.dates?.duration || this.calculateDuration(plan.startDate || plan.dates?.startDate, plan.endDate || plan.dates?.endDate),
      // 用户角色信息
      userRole,
      roleText
    }
  },

  // 格式化日期范围
  formatDateRange(startDate, endDate) {
    if (!startDate || !endDate) return ''

    const start = new Date(startDate)
    const end = new Date(endDate)

    const startMonth = start.getMonth() + 1
    const startDay = start.getDate()
    const endMonth = end.getMonth() + 1
    const endDay = end.getDate()

    if (startMonth === endMonth) {
      return `${startMonth}月${startDay}-${endDay}日`
    } else {
      return `${startMonth}月${startDay}日-${endMonth}月${endDay}日`
    }
  },

  // 计算旅行天数
  calculateDuration(startDate, endDate) {
    if (!startDate || !endDate) return 0

    const start = new Date(startDate)
    const end = new Date(endDate)
    const diffTime = Math.abs(end - start)
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1

    return diffDays
  },



  // 手动刷新数据
  manualRefresh() {
    // 显示刷新提示
    wx.showToast({
      title: '刷新中...',
      icon: 'loading',
      duration: 1000
    })

    // 强制刷新数据
    this.loadTravelData(true)
  },

  // 检查全局刷新标记
  checkGlobalRefreshFlag() {
    try {
      const refreshFlag = wx.getStorageSync('travel_page_needs_refresh')
      if (refreshFlag) {
        // 清除标记
        wx.removeStorageSync('travel_page_needs_refresh')
        return true
      }
      return false
    } catch (error) {
      return false
    }
  },

  // 探索目的地
  exploreDestinations() {
    hapticManager.navigation()
    wx.navigateTo({
      url: '/subpackages/discovery/index'
    })
  },

  // 查看旅行数据统计 - 功能已移除
  viewTravelStatistics() {
    wx.showToast({
      title: '功能开发中',
      icon: 'none'
    })
  },

  // 长按统计区域清理数据（隐藏功能）
  onLongPressStats() {
    wx.showModal({
      title: '数据清理',
      content: '检测到可能的异常数据，是否清理缓存数据？',
      confirmText: '清理',
      cancelText: '取消',
      success: async (res) => {
        if (res.confirm) {
          try {
            // 清理旅行相关缓存
            simpleDataManager.clearCache('travel')

            // 重新加载数据
            await this.loadTravelData(true)

            wx.showToast({
              title: '数据已清理',
              icon: 'success'
            })
          } catch (error) {
            wx.showToast({
              title: '清理失败',
              icon: 'none'
            })
          }
        }
      }
    })
  },

  // 查看所有计划
  viewAllPlans() {
    // 这里可以跳转到计划列表页面，或者展开显示更多计划
    wx.showToast({
      title: '功能开发中',
      icon: 'none'
    })
  },

  // 查看计划详情
  viewPlanDetail(e) {
    const plan = e.currentTarget.dataset.plan
    wx.navigateTo({
      url: `/subpackages/travel-planning/plan-detail/index?id=${plan._id || plan.id}`
    })
  },

  // {{ AURA-X: Add - 自动更新所有计划状态. Approval: 寸止(ID:1738056000). }}
  // 自动更新所有计划状态
  async autoUpdateAllPlanStatus() {
    // 防重复调用 - 每小时最多执行一次
    const now = Date.now()
    const lastUpdateTime = wx.getStorageSync('lastPlanStatusUpdate') || 0
    if (now - lastUpdateTime < 60 * 60 * 1000) { // 1小时
      console.log('计划状态更新跳过，距离上次更新不足1小时')
      return
    }

    try {
      console.log('开始批量更新计划状态')
      // 使用云函数批量更新状态
      await wx.cloud.callFunction({
        name: 'travel',
        data: {
          action: 'batchUpdatePlanStatus'
        }
      })

      // 记录更新时间
      wx.setStorageSync('lastPlanStatusUpdate', now)
    } catch (error) {
      // 静默处理错误，不影响用户体验
      console.log('自动状态更新失败:', error)
    }
  },

  // 分享页面
  onShareAppMessage() {
    return {
      title: '爱巢小记 - 旅行规划',
      path: '/subpackages/travel-planning/index',
      imageUrl: '/images/share-travel.jpg'
    }
  },

  // 分享到朋友圈
  onShareTimeline() {
    return {
      title: '爱巢小记 - 精彩旅行规划',
      imageUrl: '/images/share-travel.jpg'
    }
  },

  /**
   * 设置数据同步监听器（简化版）
   */
  setupDataSync() {
    // 使用简化数据管理器的变更监听
    simpleDataManager.onChange('travel_plans', (data) => {
      this.onTravelPlansUpdate(data)
    })
  },

  /**
   * 旅行计划列表更新回调
   * @param {Array} plans 计划列表
   */
  onTravelPlansUpdate(plans) {
    if (Array.isArray(plans)) {
      const formattedPlans = plans.map(plan => this.formatPlanData(plan))
      this.setData({
        plans: formattedPlans,
        'analytics.overview.totalPlans': plans.length
      })
    }
  },

  /**
   * 当前计划更新回调
   * @param {Object} plan 当前计划
   */
  onCurrentPlanUpdate(plan) {
    if (plan) {
      const formattedPlan = this.formatPlanData(plan)
      this.setData({
        currentPlan: formattedPlan
      })
    } else {
      this.setData({
        currentPlan: null
      })
    }
  },

  /**
   * 进行中计划更新回调
   * @param {Array} plans 进行中计划列表
   */
  onOngoingPlansUpdate(plans) {
    if (Array.isArray(plans)) {
      const formattedPlans = plans.map(plan => this.formatPlanData(plan))
      this.setData({
        ongoingPlans: formattedPlans,
        'analytics.overview.activePlans': plans.length
      })
    }
  }
})
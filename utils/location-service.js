// utils/location-service.js
// 位置服务 - 集成腾讯位置服务插件

class LocationService {
  constructor() {
    // 腾讯位置服务密钥（已更新为真实密钥）
    this.key = 'J6MBZ-CSYWW-CXHRT-34E27-DCAUS-7YFZX' // 腾讯位置服务API密钥
    this.referer = '爱巢小记' // 应用名称

    // 插件引用
    this.routePlan = null
    this.citySelector = null
    this.chooseLocation = null

    // {{ AURA-X: Modify - 更新插件版本信息. Approval: 寸止(ID:**********). }}
    // 插件版本信息
    this.pluginVersions = {
      routePlan: '2.0.6',
      citySelector: '1.0.4',
      chooseLocation: '1.1.2'
    }

    this.initPlugins()
  }

  /**
   * 初始化插件（增强错误处理）
   */
  initPlugins() {
    try {
      // {{ AURA-X: Modify - 增强插件初始化错误处理. Approval: 寸止(ID:**********). }}
      this.routePlan = requirePlugin('routePlan')
      console.log('路线规划插件初始化成功，版本:', this.pluginVersions.routePlan)
    } catch (error) {
      console.warn('路线规划插件初始化失败:', error)
      this.routePlan = null
    }

    try {
      this.citySelector = requirePlugin('citySelector')
      console.log('城市选择插件初始化成功，版本:', this.pluginVersions.citySelector)
    } catch (error) {
      console.warn('城市选择插件初始化失败:', error)
      this.citySelector = null
    }

    try {
      this.chooseLocation = requirePlugin('chooseLocation')
      console.log('位置选择插件初始化成功，版本:', this.pluginVersions.chooseLocation)
    } catch (error) {
      console.warn('位置选择插件初始化失败:', error)
      this.chooseLocation = null
    }
  }

  /**
   * 打开城市选择器
   * @param {Object} options 选项
   * @returns {Promise} 选择结果
   */
  async openCitySelector(options = {}) {
    try {
      const {
        hotCitys = '北京,上海,广州,深圳,成都,杭州,西安,南京'
      } = options

      return new Promise((resolve, reject) => {
        wx.navigateTo({
          url: `plugin://citySelector/index?key=${this.key}&referer=${this.referer}&hotCitys=${hotCitys}`,
          success: () => {
            resolve({ success: true })
          },
          fail: (error) => {
            console.error('打开城市选择器失败:', error)
            reject(error)
          }
        })
      })
    } catch (error) {
      console.error('城市选择器调用失败:', error)
      return { success: false, message: error.message }
    }
  }

  /**
   * 获取城市选择结果
   * @returns {Object|null} 城市信息
   */
  getCitySelectResult() {
    try {
      if (this.citySelector) {
        return this.citySelector.getCity()
      }
      return null
    } catch (error) {
      console.error('获取城市选择结果失败:', error)
      return null
    }
  }

  /**
   * 清空城市选择结果
   */
  clearCitySelectResult() {
    try {
      if (this.citySelector) {
        this.citySelector.clearCity()
      }
    } catch (error) {
      console.error('清空城市选择结果失败:', error)
    }
  }

  /**
   * 打开地图选点
   * @param {Object} options 选项
   * @returns {Promise} 选择结果
   */
  async openLocationPicker(options = {}) {
    try {
      const {
        latitude = null,
        longitude = null,
        category = '生活服务,娱乐休闲,旅游景点'
      } = options

      let url = `plugin://chooseLocation/index?key=${this.key}&referer=${this.referer}&category=${category}`
      
      if (latitude && longitude) {
        const location = JSON.stringify({ latitude, longitude })
        url += `&location=${location}`
      }

      return new Promise((resolve, reject) => {
        wx.navigateTo({
          url: url,
          success: () => {
            resolve({ success: true })
          },
          fail: (error) => {
            console.error('打开地图选点失败:', error)
            reject(error)
          }
        })
      })
    } catch (error) {
      console.error('地图选点调用失败:', error)
      return { success: false, message: error.message }
    }
  }

  /**
   * 获取地图选点结果
   * @returns {Object|null} 位置信息
   */
  getLocationPickResult() {
    try {
      if (this.chooseLocation) {
        return this.chooseLocation.getLocation()
      }
      return null
    } catch (error) {
      console.error('获取地图选点结果失败:', error)
      return null
    }
  }

  /**
   * 打开路线规划 (支持2.0.6版本新功能)
   * @param {Object} options 路线选项
   * @returns {Promise} 规划结果
   */
  async openRoutePlan(options = {}) {
    try {
      // {{ AURA-X: Modify - 增强插件可用性检查和错误处理. Approval: 寸止(ID:**********). }}
      // 检查插件是否可用
      if (!this.routePlan) {
        console.warn('路线规划插件不可用，使用降级方案')
        return this.fallbackRoutePlan(options)
      }

      const {
        startPoint = null,
        endPoint,
        navigation = 1, // 2.0.6版本默认开启导航
        mode = 'driving', // 新增：出行方式 driving/walking/transit
        policy = 'LEAST_TIME' // 新增：路线策略
      } = options

      if (!endPoint) {
        throw new Error('终点信息不能为空')
      }

      let url = `plugin://routePlan/index?key=${this.key}&referer=${this.referer}&navigation=${navigation}`

      // 2.0.6版本新增参数
      url += `&mode=${mode}&policy=${policy}`

      if (startPoint) {
        url += `&startPoint=${encodeURIComponent(JSON.stringify(startPoint))}`
      }

      url += `&endPoint=${encodeURIComponent(JSON.stringify(endPoint))}`

      return new Promise((resolve, reject) => {
        wx.navigateTo({
          url: url,
          success: () => {
            resolve({
              success: true,
              version: this.pluginVersions.routePlan,
              features: ['navigation', 'multiMode', 'routePolicy', 'enhancedError']
            })
          },
          fail: (error) => {
            console.error('打开路线规划失败:', error)
            // 插件调用失败时使用降级方案
            this.fallbackRoutePlan(options).then(resolve).catch(reject)
          }
        })
      })
    } catch (error) {
      console.error('路线规划调用失败:', error)
      return this.fallbackRoutePlan(options)
    }
  }

  /**
   * 路线规划降级方案
   * @param {Object} options 路线选项
   * @returns {Promise} 降级结果
   */
  async fallbackRoutePlan(options = {}) {
    const { endPoint } = options

    // 使用微信原生地图打开位置
    return new Promise((resolve) => {
      wx.openLocation({
        latitude: endPoint.latitude,
        longitude: endPoint.longitude,
        name: endPoint.name || '目的地',
        address: endPoint.address || '',
        success: () => {
          resolve({
            success: true,
            fallback: true,
            message: '已使用原生地图打开位置'
          })
        },
        fail: (error) => {
          console.error('原生地图打开失败:', error)
          resolve({
            success: false,
            fallback: true,
            message: '地图功能暂时不可用'
          })
        }
      })
    })
  }

  /**
   * 获取路线规划结果 (2.0.5版本新增)
   * @returns {Object|null} 路线信息
   */
  getRouteResult() {
    try {
      if (this.routePlan && this.routePlan.getRoute) {
        return this.routePlan.getRoute()
      }
      return null
    } catch (error) {
      console.error('获取路线规划结果失败:', error)
      return null
    }
  }

  /**
   * 获取当前位置
   * @returns {Promise} 位置信息
   */
  async getCurrentLocation() {
    try {
      // 获取用户授权
      await new Promise((resolve, reject) => {
        wx.authorize({
          scope: 'scope.userLocation',
          success: resolve,
          fail: reject
        })
      })

      // 获取位置信息
      const location = await new Promise((resolve, reject) => {
        wx.getLocation({
          type: 'gcj02',
          success: resolve,
          fail: reject
        })
      })

      return {
        success: true,
        data: {
          latitude: location.latitude,
          longitude: location.longitude,
          accuracy: location.accuracy
        }
      }
    } catch (error) {
      console.error('获取位置失败:', error)
      return {
        success: false,
        message: error.message || '获取位置失败'
      }
    }
  }

  /**
   * 计算两点间距离
   * @param {Object} point1 起点 {latitude, longitude}
   * @param {Object} point2 终点 {latitude, longitude}
   * @returns {number} 距离（米）
   */
  calculateDistance(point1, point2) {
    const R = 6371000 // 地球半径（米）
    const lat1Rad = point1.latitude * Math.PI / 180
    const lat2Rad = point2.latitude * Math.PI / 180
    const deltaLatRad = (point2.latitude - point1.latitude) * Math.PI / 180
    const deltaLngRad = (point2.longitude - point1.longitude) * Math.PI / 180

    const a = Math.sin(deltaLatRad / 2) * Math.sin(deltaLatRad / 2) +
              Math.cos(lat1Rad) * Math.cos(lat2Rad) *
              Math.sin(deltaLngRad / 2) * Math.sin(deltaLngRad / 2)
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a))

    return R * c
  }

  /**
   * 获取热门城市列表
   * @returns {Array} 热门城市
   */
  getPopularCities() {
    return [
      { name: '北京', province: '北京市', city: '北京市', coordinates: { latitude: 39.9042, longitude: 116.4074 } },
      { name: '上海', province: '上海市', city: '上海市', coordinates: { latitude: 31.2304, longitude: 121.4737 } },
      { name: '广州', province: '广东省', city: '广州市', coordinates: { latitude: 23.1291, longitude: 113.2644 } },
      { name: '深圳', province: '广东省', city: '深圳市', coordinates: { latitude: 22.5431, longitude: 114.0579 } },
      { name: '杭州', province: '浙江省', city: '杭州市', coordinates: { latitude: 30.2741, longitude: 120.1551 } },
      { name: '成都', province: '四川省', city: '成都市', coordinates: { latitude: 30.5728, longitude: 104.0668 } },
      { name: '西安', province: '陕西省', city: '西安市', coordinates: { latitude: 34.3416, longitude: 108.9398 } },
      { name: '南京', province: '江苏省', city: '南京市', coordinates: { latitude: 32.0603, longitude: 118.7969 } },
      { name: '武汉', province: '湖北省', city: '武汉市', coordinates: { latitude: 30.5928, longitude: 114.3055 } },
      { name: '厦门', province: '福建省', city: '厦门市', coordinates: { latitude: 24.4798, longitude: 118.0819 } },
      { name: '青岛', province: '山东省', city: '青岛市', coordinates: { latitude: 36.0671, longitude: 120.3826 } },
      { name: '大理', province: '云南省', city: '大理市', coordinates: { latitude: 25.6064, longitude: 100.2675 } }
    ]
  }

  /**
   * 验证API密钥
   * @returns {Object} 验证结果
   */
  validateApiKey() {
    const keyPattern = /^[A-Z0-9]{5}-[A-Z0-9]{5}-[A-Z0-9]{5}-[A-Z0-9]{5}-[A-Z0-9]{5}-[A-Z0-9]{4}$/
    const isValid = keyPattern.test(this.key)

    return {
      isValid: isValid,
      key: this.key,
      format: isValid ? '正确' : '格式错误',
      message: isValid ? 'API密钥格式正确' : 'API密钥格式不正确，请检查'
    }
  }

  /**
   * 获取插件版本信息
   * @returns {Object} 版本信息
   */
  getPluginVersions() {
    return {
      ...this.pluginVersions,
      apiKey: this.validateApiKey(),
      updateTime: '2025-01-24',
      features: {
        routePlan: ['导航功能', '多种出行方式', '路线策略选择', '实时路况'],
        citySelector: ['热门城市自定义', '搜索功能', '拼音索引', '组件模式'],
        chooseLocation: ['POI分类', '搜索功能', '位置详情', '地址解析']
      }
    }
  }

  /**
   * 检查插件兼容性
   * @returns {Object} 兼容性检查结果
   */
  checkPluginCompatibility() {
    const compatibility = {
      routePlan: true,
      citySelector: true,
      chooseLocation: true,
      issues: []
    }

    try {
      // 检查插件是否正确加载
      if (!this.routePlan) {
        compatibility.routePlan = false
        compatibility.issues.push('路线规划插件未正确加载')
      }

      if (!this.citySelector) {
        compatibility.citySelector = false
        compatibility.issues.push('城市选择器插件未正确加载')
      }

      if (!this.chooseLocation) {
        compatibility.chooseLocation = false
        compatibility.issues.push('地图选点插件未正确加载')
      }

      return {
        success: compatibility.issues.length === 0,
        data: compatibility
      }
    } catch (error) {
      return {
        success: false,
        message: '插件兼容性检查失败',
        error: error.message
      }
    }
  }

  /**
   * 批量地点搜索（使用地图选点插件的搜索功能）
   * @param {Array} keywords 关键词列表
   * @returns {Promise} 搜索结果
   */
  async batchLocationSearch(keywords) {
    try {
      const results = []

      for (const keyword of keywords) {
        // 使用地图选点插件进行搜索
        const searchResult = await this.openLocationPicker({
          category: '旅游景点,生活服务,娱乐休闲',
          searchKeyword: keyword // 1.1.1版本新增搜索关键词参数
        })

        if (searchResult.success) {
          results.push({
            keyword: keyword,
            result: searchResult
          })
        }
      }

      return {
        success: true,
        data: results
      }
    } catch (error) {
      console.error('批量地点搜索失败:', error)
      return {
        success: false,
        message: error.message
      }
    }
  }

  /**
   * 模拟地点搜索（降级方案）
   * @param {string} keyword 搜索关键词
   * @returns {Object} 搜索结果
   */
  getMockSearchResults(keyword) {
    const mockData = [
      {
        id: `mock_${Date.now()}_1`,
        name: `${keyword}市`,
        address: `中国某省${keyword}市`,
        province: '某省',
        city: `${keyword}市`,
        district: '某区',
        coordinates: {
          latitude: 39.9042 + Math.random() * 0.1,
          longitude: 116.4074 + Math.random() * 0.1
        },
        category: '行政区划'
      },
      {
        id: `mock_${Date.now()}_2`,
        name: `${keyword}景区`,
        address: `中国某省某市${keyword}景区`,
        province: '某省',
        city: '某市',
        district: '某区',
        coordinates: {
          latitude: 39.9042 + Math.random() * 0.1,
          longitude: 116.4074 + Math.random() * 0.1
        },
        category: '旅游景点'
      }
    ]

    return {
      success: true,
      data: mockData,
      isMock: true
    }
  }
}

// 导出单例
const locationService = new LocationService()
export default locationService

/**
 * 智能头像管理器
 * 渐进式加载：默认头像 → 缓存头像 → 最新头像
 * 后台更新缓存，下次访问生效
 */

class SmartAvatarManager {
  constructor() {
    // 内存缓存
    this.memoryCache = new Map()

    // 本地存储缓存键前缀
    this.storagePrefix = 'avatar_cache_'

    // 缓存配置
    this.config = {
      // {{ AURA-X: Modify - 优化缓存配置提升头像加载稳定性. Approval: 寸止(ID:1738056000). }}
      // 内存缓存有效期：5分钟（减少，因为临时链接容易过期）
      memoryCacheTTL: 5 * 60 * 1000,
      // 本地存储缓存有效期：2小时（大幅减少，避免临时链接过期）
      storageCacheTTL: 2 * 60 * 60 * 1000,
      // 最大重试次数
      maxRetries: 3,
      // 重试延迟
      retryDelay: 1000,
      // 默认头像
      defaultAvatar: '/images/user.svg',
      // 临时链接有效期检测阈值（1.5小时）
      tempUrlValidityThreshold: 1.5 * 60 * 60 * 1000
    }

    // 正在加载的头像（防止重复请求）
    this.loadingAvatars = new Set()

    // 更新队列（后台更新）
    this.updateQueue = new Set()
  }

  /**
   * 渐进式获取头像URL（增强版）
   * @param {string} fileID 云存储文件ID
   * @param {string} openid 用户openid（用于缓存键）
   * @param {boolean} forceRefresh 强制刷新（用户换头像时）
   * @returns {Promise<string>} 头像URL
   */
  async getAvatarUrl(fileID, openid, forceRefresh = false) {
    try {
      // 如果不是云存储文件，直接返回
      if (!fileID || !fileID.startsWith('cloud://')) {
        return fileID || this.config.defaultAvatar
      }

      const cacheKey = this.getCacheKey(fileID, openid)

      // 强制刷新时，清除缓存并后台更新
      if (forceRefresh) {
        this.memoryCache.delete(cacheKey)
        this.clearStorageCache(cacheKey)
        // 后台更新，不阻塞UI
        this.backgroundUpdate(fileID, cacheKey)
        return this.config.defaultAvatar
      }

      // 1. 优先返回内存缓存
      const memoryUrl = this.getFromMemoryCache(cacheKey)
      if (memoryUrl) {
        // {{ AURA-X: Add - 验证内存缓存URL的有效性. Approval: 寸止(ID:1738056000). }}
        if (await this.validateUrl(memoryUrl)) {
          return memoryUrl
        } else {
          // URL无效，清除缓存
          this.memoryCache.delete(cacheKey)
        }
      }

      // 2. 返回本地存储缓存
      const storageUrl = this.getFromStorageCache(cacheKey)
      if (storageUrl) {
        // 验证URL有效性
        if (await this.validateUrl(storageUrl)) {
          // 更新内存缓存
          this.setMemoryCache(cacheKey, storageUrl)
          // 后台检查更新
          this.backgroundUpdate(fileID, cacheKey)
          return storageUrl
        } else {
          // URL无效，清除缓存
          this.clearStorageCache(cacheKey)
        }
      }

      // 3. 返回默认头像，后台加载真实头像
      this.backgroundUpdate(fileID, cacheKey)
      return this.config.defaultAvatar

    } catch (error) {
      console.error('获取头像URL失败:', error)
      return this.config.defaultAvatar
    }
  }

  /**
   * 验证URL有效性（简单检查）
   * @param {string} url URL地址
   * @returns {Promise<boolean>} 是否有效
   */
  async validateUrl(url) {
    try {
      // 对于云存储临时链接，检查是否包含过期参数
      if (url.includes('tcb.qcloud.la') && url.includes('&t=')) {
        const match = url.match(/&t=(\d+)/)
        if (match) {
          const expireTime = parseInt(match[1]) * 1000
          const now = Date.now()
          // 如果链接在30分钟内过期，认为无效
          if (expireTime - now < 30 * 60 * 1000) {
            return false
          }
        }
      }
      return true
    } catch (error) {
      return false
    }
  }

  /**
   * 后台更新头像
   */
  async backgroundUpdate(fileID, cacheKey) {
    // 防止重复更新
    if (this.updateQueue.has(cacheKey)) {
      return
    }

    this.updateQueue.add(cacheKey)

    try {
      const tempUrl = await this.fetchTempUrl(fileID)

      if (tempUrl) {
        // 更新缓存
        this.setMemoryCache(cacheKey, tempUrl)
        this.setStorageCache(cacheKey, tempUrl)
        console.log(`头像后台更新成功: ${cacheKey}`)
      }
    } catch (error) {
      console.warn(`头像后台更新失败: ${cacheKey}`, error)
    } finally {
      this.updateQueue.delete(cacheKey)
    }
  }

  /**
   * 从云端获取临时链接（增强版）
   * @param {string} fileID 云存储文件ID
   * @returns {Promise<string>} 临时链接
   */
  async fetchTempUrl(fileID) {
    let retries = 0

    while (retries < this.config.maxRetries) {
      try {
        // {{ AURA-X: Modify - 增强头像临时链接获取的稳定性. Approval: 寸止(ID:1738056000). }}
        // 1. 首先尝试直接使用云存储API
        const result = await wx.cloud.getTempFileURL({
          fileList: [fileID]
        })

        if (result.fileList && result.fileList[0]) {
          const fileInfo = result.fileList[0]

          // 检查是否获取成功
          if (fileInfo.tempFileURL && fileInfo.status === 0) {
            return fileInfo.tempFileURL
          }

          // 如果状态不为0，说明文件可能不存在或权限问题
          if (fileInfo.status !== 0) {
            console.warn(`文件状态异常: ${fileInfo.status}, ${fileInfo.errMsg}`)
            // 文件不存在或权限问题，不再重试
            return null
          }
        }

        throw new Error('获取临时链接失败')

      } catch (error) {
        retries++
        console.warn(`获取头像临时链接失败 (${retries}/${this.config.maxRetries}):`, error)

        // 如果是网络连接错误，增加重试延迟
        const isNetworkError = error.message.includes('ERR_CONNECTION_CLOSED') ||
                              error.message.includes('network')

        if (retries < this.config.maxRetries) {
          const delay = isNetworkError ? this.config.retryDelay * 2 : this.config.retryDelay
          await this.delay(delay)
        }
      }
    }

    return null
  }

  /**
   * 生成缓存键
   * @param {string} fileID 文件ID
   * @param {string} openid 用户openid
   * @returns {string} 缓存键
   */
  getCacheKey(fileID, openid) {
    // 使用文件ID的哈希值作为缓存键，避免键过长
    const hash = this.simpleHash(fileID)
    return `${openid}_${hash}`
  }

  /**
   * 简单哈希函数
   * @param {string} str 字符串
   * @returns {string} 哈希值
   */
  simpleHash(str) {
    let hash = 0
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i)
      hash = ((hash << 5) - hash) + char
      hash = hash & hash // 转换为32位整数
    }
    return Math.abs(hash).toString(36)
  }

  /**
   * 从内存缓存获取
   * @param {string} key 缓存键
   * @returns {string|null} 缓存的URL
   */
  getFromMemoryCache(key) {
    const cached = this.memoryCache.get(key)
    if (!cached) return null

    // 检查是否过期
    if (Date.now() - cached.timestamp > this.config.memoryCacheTTL) {
      this.memoryCache.delete(key)
      return null
    }

    return cached.url
  }

  /**
   * 设置内存缓存
   * @param {string} key 缓存键
   * @param {string} url URL
   */
  setMemoryCache(key, url) {
    this.memoryCache.set(key, {
      url,
      timestamp: Date.now()
    })

    // 限制内存缓存大小
    if (this.memoryCache.size > 100) {
      const firstKey = this.memoryCache.keys().next().value
      this.memoryCache.delete(firstKey)
    }
  }

  /**
   * 从本地存储缓存获取（增强版）
   * @param {string} key 缓存键
   * @returns {string|null} 缓存的URL
   */
  getFromStorageCache(key) {
    try {
      const storageKey = this.storagePrefix + key
      const cached = wx.getStorageSync(storageKey)

      if (!cached) return null

      const data = JSON.parse(cached)

      // {{ AURA-X: Modify - 增强缓存有效性检测. Approval: 寸止(ID:1738056000). }}
      // 检查是否过期
      if (Date.now() - data.timestamp > this.config.storageCacheTTL) {
        wx.removeStorageSync(storageKey)
        return null
      }

      // 检查临时链接是否可能已过期（基于时间推测）
      if (data.url && data.url.includes('tcb.qcloud.la')) {
        const timeSinceCache = Date.now() - data.timestamp
        if (timeSinceCache > this.config.tempUrlValidityThreshold) {
          // 临时链接可能已过期，清除缓存并触发后台更新
          wx.removeStorageSync(storageKey)
          return null
        }
      }

      return data.url

    } catch (error) {
      console.error('读取本地存储缓存失败:', error)
      return null
    }
  }

  /**
   * 设置本地存储缓存
   * @param {string} key 缓存键
   * @param {string} url URL
   */
  setStorageCache(key, url) {
    try {
      const storageKey = this.storagePrefix + key
      const data = {
        url,
        timestamp: Date.now()
      }

      wx.setStorageSync(storageKey, JSON.stringify(data))

    } catch (error) {
      console.error('设置本地存储缓存失败:', error)
    }
  }

  /**
   * 清除单个本地存储缓存
   * @param {string} key 缓存键
   */
  clearStorageCache(key) {
    try {
      const storageKey = this.storagePrefix + key
      wx.removeStorageSync(storageKey)
    } catch (error) {
      console.error('清除本地存储缓存失败:', error)
    }
  }

  /**
   * 等待加载完成
   * @param {string} key 缓存键
   * @returns {Promise<string>} URL
   */
  async waitForLoading(key) {
    let attempts = 0
    const maxAttempts = 30 // 最多等待3秒

    while (this.loadingAvatars.has(key) && attempts < maxAttempts) {
      await this.delay(100)
      attempts++
    }

    // 再次尝试从缓存获取
    return this.getFromMemoryCache(key) || '/images/user.svg'
  }

  /**
   * 延迟函数
   * @param {number} ms 毫秒
   * @returns {Promise}
   */
  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms))
  }

  /**
   * 清理过期缓存
   */
  cleanupExpiredCache() {
    // 清理内存缓存
    const now = Date.now()
    for (const [key, cached] of this.memoryCache.entries()) {
      if (now - cached.timestamp > this.config.memoryCacheTTL) {
        this.memoryCache.delete(key)
      }
    }

    // 清理本地存储缓存（异步执行，不阻塞主线程）
    setTimeout(() => {
      try {
        const info = wx.getStorageInfoSync()
        info.keys.forEach(key => {
          if (key.startsWith(this.storagePrefix)) {
            try {
              const cached = wx.getStorageSync(key)
              const data = JSON.parse(cached)
              
              if (now - data.timestamp > this.config.storageCacheTTL) {
                wx.removeStorageSync(key)
              }
            } catch (error) {
              // 删除损坏的缓存
              wx.removeStorageSync(key)
            }
          }
        })
      } catch (error) {
        console.error('清理本地存储缓存失败:', error)
      }
    }, 0)
  }

  /**
   * 预热头像缓存
   * @param {Array} avatarList 头像列表 [{fileID, openid}]
   */
  async warmupCache(avatarList) {
    if (!Array.isArray(avatarList) || avatarList.length === 0) {
      return
    }

    // 限制并发数量，避免过多请求
    const concurrency = 3
    const chunks = []
    
    for (let i = 0; i < avatarList.length; i += concurrency) {
      chunks.push(avatarList.slice(i, i + concurrency))
    }

    for (const chunk of chunks) {
      await Promise.allSettled(
        chunk.map(({ fileID, openid }) => this.getAvatarUrl(fileID, openid))
      )
    }
  }

  /**
   * 清空所有缓存
   */
  clearAllCache() {
    // 清空内存缓存
    this.memoryCache.clear()

    // 清空本地存储缓存
    try {
      const info = wx.getStorageInfoSync()
      info.keys.forEach(key => {
        if (key.startsWith(this.storagePrefix)) {
          wx.removeStorageSync(key)
        }
      })
    } catch (error) {
      console.error('清空本地存储缓存失败:', error)
    }
  }

  /**
   * 获取缓存统计
   * @returns {Object} 缓存统计信息
   */
  getCacheStats() {
    let storageCount = 0
    try {
      const info = wx.getStorageInfoSync()
      storageCount = info.keys.filter(key => key.startsWith(this.storagePrefix)).length
    } catch (error) {
      console.error('获取存储缓存统计失败:', error)
    }

    return {
      memoryCache: this.memoryCache.size,
      storageCache: storageCount,
      loadingCount: this.loadingAvatars.size
    }
  }
}

// 创建全局实例
const smartAvatarManager = new SmartAvatarManager()

// 定期清理过期缓存（每30分钟，减少频率）
setInterval(() => {
  smartAvatarManager.cleanupExpiredCache()
}, 30 * 60 * 1000)

export default smartAvatarManager
export { SmartAvatarManager }
